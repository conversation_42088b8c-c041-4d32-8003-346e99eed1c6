import { defHttp } from '/@/utils/http/axios';

enum Api {
    // getIncomeAndDownloadByType = '/shop/getIncomeAndDownloadByType',
    getIncomeAndDownloadGroupByGenre = '/shop/topStore/getIncomeAndDownloadGroupByGenre',
    shopBehavior = '/shop/shopBehavior',
    getAllGenre = '/shop/getAllGenre',
    getAllDevice = '/shop/getAllDevice',
    // queryByParam = '/downloadTimesAndRevenue/queryByParam',
    top = '/shop/topStore/top',
    currentMarket = '/shop/topStore/currentMarket',
    moduleAnalysis = '/smartReadingDB/moduleAnalysis',
    getAllCountry = '/shop/getAllCountry',

}

// 获取用户数据
export function getIncomeAndDownloadGroupByGenreApi(data){
    return defHttp.post({
        url: Api.getIncomeAndDownloadGroupByGenre,
        data
    })
}

// 获取三个榜单数据
export function topApi(data){
    return defHttp.post({
        url:Api.top,
        data
    })
}

// 获取输入框游戏类型
export function getAllGenreApi(){
    return defHttp.get({
        url: Api.getAllGenre,
    })
}

// 获取输入框国家类型
export function getAllCountryApi(){
    return defHttp.get({
        url: Api.getAllCountry,
    })
}

// 获取输入框游戏平台类型
export function getAllDeviceApi(){
    return defHttp.get({
        url: Api.getAllDevice,
    })
}

// 获得市场现状数据
export function currentMarketApi(data){
    return defHttp.post({
        url: Api.currentMarket,
        data
    })
}

// AI分析接口
export function moduleAnalysisApi(data: any) {
  return defHttp.post({
    url: Api.moduleAnalysis,
    data,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream'
    },
    responseType: 'stream' // 根据实际HTTP库支持情况可能需要调整
  });
}



