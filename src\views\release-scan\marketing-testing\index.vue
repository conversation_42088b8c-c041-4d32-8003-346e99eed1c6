<template>
  <div class="chart-container">
    <!-- 顶部栏：添加游戏 -->
    <div class="top-bar">
      <div
        v-for="(game, index) in games"
        :key="game.value"
        class="game-details"
      >
        <div class="logo-container">
          <img
            :src="game.icon"
            :alt="game.label"
            class="logo"
          />
          <button
            class="delete-button"
            @click="deleteGame(index)"
          >-</button>
        </div>
        <p
          class="game-title"
          :title="game.label"
        >{{ game.label && game.label.length > 15 ? game.label.slice(0, 15) + '...' : game.label }}</p>
      </div>
      <div
        class="add-button-wrapper"
        @click="showModal"
      >
        <button class="add-button">+</button>
        <span class="button-label">添加游戏</span>
      </div>
    </div>
    <!-- 游戏添加弹窗 -->
    <a-modal
      v-model:visible="isModalVisible"
      title="添加游戏"
      @ok="handleOk"
      @cancel="handleCancel"
      :bodyStyle="{ minHeight: '350px' }"
    >
      <a-select
        v-model:value="selectedGames"
        mode="multiple"
        :options="gameOptions"
        :field-names="{ label: 'label', value: 'value' }"
        placeholder="请选择要添加的游戏"
        style="width: 90%;margin: 24px 2% 0 10px;"
        :option-label-prop="'label'"
        :dropdown-render="dropdownRender"
        :filter-option="false"
        :show-search="true"
        :loading="gameLoading"
        :pagination="false"
        @search="onGameSearch"
      >
        <template #option="{ label, icon }">
          <div style="display: flex; align-items: center;">
            <img
              :src="icon"
              alt=""
              style="width: 24px; height: 24px; margin-right: 8px;"
            />
            <span>{{ label && label.length > 30 ? label.slice(0, 30) + '...' : label }}</span>
          </div>
        </template>
      </a-select>
      <div
        v-if="gameLoading"
        style="text-align:center;margin-top:10px;"
      >
        <a-spin />
      </div>
    </a-modal>
  </div>

  <div class="chart-container">
    <div class="chart-containertitle">推广活动热度统计</div>
    <div class="chart-container1">
      <!-- 年份选择 -->
      <a-select
        v-model:value="selectedYear"
        placeholder="请选择年份"
        style="width: 120px;"
      >
        <a-select-option
          v-for="year in yearOptions"
          :key="year"
          :value="year"
        >{{ year }}</a-select-option>
      </a-select>
      <!-- 国家选择 -->
      <a-select
        v-model:value="selectedCountry"
        mode="multiple"
        allowClear
        placeholder="请选择国家"
        style="width: 15vw;"
        :max-tag-count="1"
        :max-tag-placeholder="maxTagPlaceholder"
        @change="handleCountryChange"
      >
        <a-select-option
          value="all"
          @click="selectAllCountries"
        >选择全部</a-select-option>
        <a-select-option
          v-for="country in countries"
          :key="country.value"
          :value="country.value"
        >
          {{ country.label }}
        </a-select-option>
      </a-select>
      <!-- 平台选择 -->
      <a-select
        v-model:value="selectedDevice"
        mode="multiple"
        allowClear
        placeholder="请选择平台"
        style="width: 15vw;"
        :max-tag-count="1"
        :max-tag-placeholder="maxTagPlaceholder"
        @change="handleDeviceChange"
      >
        <a-select-option
          value="all"
          @click="selectAllDevices"
        >选择全部</a-select-option>
        <a-select-option
          v-for="device in devices"
          :key="device.value"
          :value="device.value"
        >
          {{ device.label }}
        </a-select-option>
      </a-select>
      <!-- 统计维度选择 -->
      <a-select
        v-model:value="selectedMetric"
        placeholder="请选择统计维度"
        style="width: 160px;"
      >
        <a-select-option
          v-for="option in metricOptions"
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </a-select-option>
      </a-select>
      <!-- 查询按钮 -->
      <a-button
        type="primary"
        @click="handleQuery"
        style="margin-left: 20px;"
      >查询</a-button>
    </div>
    <!-- 柱状图 -->
    <div style="position:relative;">
      <div
        ref="chartRef"
        style="width: 1000px; height: 500px; margin: auto;"
      ></div>
      <div
        v-if="chartNoData"
        class="no-data-container"
      >
        当前筛选条件下无数据
      </div>
      <div
        v-if="!hasQueried"
        style="position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:1000px;height:500px;display:flex;flex-direction:column;align-items:center;justify-content:center;z-index:10;background:rgba(255,255,255,0.85);"
      >
        <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
        <div style="font-size: 20px; color: #666; margin-bottom: 10px;">请选择游戏并点击查询</div>
        <div style="font-size: 14px; color: #999;">最多可选择5款游戏进行比较</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, h } from 'vue';
import * as echarts from 'echarts';
import { message } from 'ant-design-vue';
import { getAllDeviceApi, getMutDimApi, getAllCountryApi, getPopularityApi } from '@/api/release-scan/marketing-testing/index';
import { findGamesByPrefixApi } from '@/api/public-opinion-monitoring';

// 游戏相关
const isModalVisible = ref(false);
const selectedGames = ref<string[]>([]);
const gameOptions = ref<{ label: string; value: string; icon: string; gameId: string }[]>([]);
const games = ref<{ label: string; value: string; icon: string; gameId: string }[]>([]);
const gameLoading = ref(false);
const gameSearchKeyword = ref('');
const maxGames = 5; // 最大游戏数量限制
let searchDebounceTimer: ReturnType<typeof setTimeout> | null = null;
// 年份、国家、平台、统计维度
const selectedYear = ref(new Date().getFullYear());
const yearOptions = Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i);
const selectedCountry = ref<string[]>([]);
const selectedDevice = ref<string[]>([]);
const selectedMetric = ref('下载量');
const metricOptions = [
  { value: '下载量', label: '下载量' },
  { value: '收入情况', label: '收入情况' },
  // { value: '平台得分', label: '平台得分' },
  // { value: '用户留存率', label: '用户留存率' },
  // { value: '活跃用户数', label: '活跃用户数' },
];
const countries = ref<{ value: string; label: string }[]>([]);
const devices = ref<{ value: string; label: string }[]>([]);
const fetchDevices = async () => {
  try {
    const res = await getAllDeviceApi();
    devices.value = (res || []).map((item: any) => ({
      value: item.value,
      label: item.value === 'apple' ? 'App Store' : item.value === 'google' ? 'Google Play' : item.value,
    }));
  } catch (e) {
    devices.value = [];
  }
};
// 获取国家数据
const fetchCountries = async () => {
  try {
    const res = await getAllCountryApi();

    if (res && Array.isArray(res)) {
      // 过滤掉无效的国家数据
      countries.value = res
        .filter((item: any) => {
          return (
            item &&
            typeof item === 'object' &&
            item.value &&
            typeof item.value === 'string' &&
            item.value.trim() !== '' &&
            item.value !== '5' &&
            item.value !== '6'
          ); // 过滤掉数字ID
        })
        .map((item: any) => ({
          value: item.value.trim(),
          label: item.value.trim(),
        }));
    } else {
      throw new Error('API返回数据格式不正确');
    }
  } catch (e) {
    // API失败时使用默认国家列表
    countries.value = [
      { value: '菲律宾', label: '菲律宾' },
      { value: '柬埔寨', label: '柬埔寨' },
      { value: '马来西亚', label: '马来西亚' },
      { value: '泰国', label: '泰国' },
      { value: '文莱', label: '文莱' },
      { value: '新加坡', label: '新加坡' },
      { value: '印度尼西亚', label: '印度尼西亚' },
      { value: '越南', label: '越南' },
      { value: '缅甸', label: '缅甸' },
      { value: '中国台湾', label: '中国台湾' },
      { value: '老挝人民民主共和国', label: '老挝人民民主共和国' },
    ];
    console.error('获取国家数据失败:', e);
  }
};
const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
// 显示弹窗
const showModal = () => {
  isModalVisible.value = true;
  gameSearchKeyword.value = ''; // 重置搜索关键字
  fetchGameOptions(); // 初始加载全部游戏
};
const fetchGameOptions = async () => {
  gameLoading.value = true;
  try {
    const res = await findGamesByPrefixApi({
      prefix: gameSearchKeyword.value || '',
    });

    const list = res?.records || [];
    gameOptions.value = list.map((item: any) => ({
      label: item.nameZh,
      value: item.id,
      icon: item.iconUrl,
      gameId: item.id,
    }));

    // 去重：使用gameId作为唯一标识符
    const uniqueOptions = gameOptions.value.filter((option, index, self) => index === self.findIndex((t) => t.gameId === option.gameId));

    gameOptions.value = uniqueOptions;
  } catch (e) {
    message.error('获取游戏列表失败');
  } finally {
    gameLoading.value = false;
  }
};

const onGameSearch = (val: string) => {
  gameSearchKeyword.value = val;

  // 使用防抖避免频繁请求
  if (searchDebounceTimer) {
    clearTimeout(searchDebounceTimer);
  }
  searchDebounceTimer = setTimeout(() => {
    fetchGameOptions();
  }, 300);
};

const onGameScroll = (e: Event) => {
  // 前缀树API不支持分页，所以不需要滚动加载更多
  // 保留函数以避免模板报错，但不执行任何操作
};

const handleOk = () => {
  if (selectedGames.value.length + games.value.length > maxGames) {
    message.warning(`最多只能添加${maxGames}款游戏进行比较`);
    return;
  }

  // 获取选中的游戏信息，并确保不重复
  const selectedGameInfo = gameOptions.value
    .filter((option) => selectedGames.value.includes(option.value))
    .filter((option, index, self) => index === self.findIndex((t) => t.gameId === option.gameId));

  // 使用gameId进行去重
  const newGames = selectedGameInfo.filter((newGame) => !games.value.some((existingGame) => existingGame.gameId === newGame.gameId));

  if (newGames.length === 0) {
    message.info('所选游戏已全部添加');
    selectedGames.value = [];
    isModalVisible.value = false;
    return;
  }

  // 添加到games数组
  games.value = [...games.value, ...newGames];

  // 清空选择
  selectedGames.value = [];
  isModalVisible.value = false;
};
const handleCancel = () => {
  isModalVisible.value = false;
  selectedGames.value = [];
};
const deleteGame = (index: number) => {
  games.value.splice(index, 1);
};
const dropdownRender = ({ menuNode }) => menuNode;

// 柱状图
const chartRef = ref<HTMLElement | null>(null);
let chartInstance: echarts.ECharts | null = null;
onMounted(() => {
  fetchDevices();
  fetchCountries();
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value);
  }
});

// 添加状态标识
const hasQueried = ref(false);
const chartNoData = ref(false);

// 添加 maxTagPlaceholder 函数
const maxTagPlaceholder = (omittedValues: any[]) => {
  return h('span', { style: { color: '#666' } }, `+${omittedValues.length}...`);
};

// 添加选择全部函数
const selectAllCountries = () => {
  selectedCountry.value = countries.value.map((item) => item.value);
};

const selectAllDevices = () => {
  selectedDevice.value = devices.value.map((item) => item.value);
};

// 添加选择变化处理函数
const handleCountryChange = (value: string[]) => {
  if (value.includes('all')) {
    selectAllCountries();
  }
};

const handleDeviceChange = (value: string[]) => {
  if (value.includes('all')) {
    selectAllDevices();
  }
};

// 修改查询函数
const handleQuery = async () => {
  // 检查是否选择了游戏
  const gameIds = games.value.map((g) => g.gameId);
  if (!gameIds.length) {
    message.warning('请添加游戏');
    return;
  }
  if (gameIds.length > maxGames) {
    message.warning(`最多只能选择${maxGames}款游戏进行比较`);
    return;
  }
  hasQueried.value = true;

  try {
    // 根据接口文档构建参数
    const params = {
      appIdList: gameIds,
      startTime: `${selectedYear.value}-01-01`,
      endTime: `${selectedYear.value}-12-31`,
    };

    // 添加可选参数（如果用户选择了）
    if (selectedCountry.value.length > 0) {
      // 处理所有国家的特殊值
      const allCountriesValue = countries.value.map((c) => c.value);
      if (selectedCountry.value.includes('all') || selectedCountry.value.length === allCountriesValue.length) {
        params.countryNames = allCountriesValue;
      } else {
        params.countryNames = selectedCountry.value;
      }
    }

    if (selectedDevice.value.length > 0) {
      // 处理所有平台的特殊值
      const allDevicesValue = devices.value.map((d) => (d.value === 'App Store' ? 'apple' : d.value === 'Google Play' ? 'google' : d.value));
      if (selectedDevice.value.includes('all') || selectedDevice.value.length === allDevicesValue.length) {
        params.platformNames = allDevicesValue;
      } else {
        params.platformNames = selectedDevice.value.map((d) => (d === 'App Store' ? 'apple' : d === 'Google Play' ? 'google' : d));
      }
    }

    // 调用新接口
    const res = await getPopularityApi(params);
    console.log('请求参数： ', params);
    console.log('返回结果： ', res);

    // 检查返回数据
    const resultData = Array.isArray(res) ? res : [];

    // 提取当前选择的指标类型
    const metricType = selectedMetric.value === '下载量' ? 'downloads' : 'revenues';
    const hasData = resultData.some((gameData) => gameData.months?.some((month) => month[metricType] > 0));

    chartNoData.value = !hasData;
    if (!hasData) {
      if (chartInstance) {
        chartInstance.clear();
      }
      console.warn('无有效数据');
      return;
    }
    const series = resultData.map(gameData => {
      const gameInfo = games.value.find(
        game => game.value === gameData.appId // 使用appId匹配
      );
      
      const gameName = gameInfo?.label || gameData.gameName;
      const dataArray = Array(12).fill(0);

      // 确保months存在且是数组
      if (Array.isArray(gameData.months)) {
        gameData.months.forEach(monthData => {
          const monthIndex = parseInt(monthData.month) - 1;
          if (!isNaN(monthIndex) && monthIndex >= 0 && monthIndex < 12) {
            dataArray[monthIndex] = monthData[metricType] || 0;
          }
        });
      }

      return {
        name: gameName,
        type: 'line',
        data: dataArray,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: { width: 2 },
        
      };
    });

    if (!chartRef.value) return;
    if (!chartInstance) {
      chartInstance = echarts.init(chartRef.value);
    }
    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          let tip = `${params[0].name}<br/>`;
          params.forEach((item: any) => {
            tip += `${item.seriesName}: ${item.value.toLocaleString()}<br/>`;
          });
          return tip;
        },
      },
      legend: {
        data: series.map((s) => s.name),
        orient: 'vertical',
        right: 10,
        top: 'center',
      },
      grid: {
        left: '3%',
        right: '15%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: months,
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: (value: number) => (value >= 10000 ? `${(value / 10000).toFixed(1)}万` : value.toLocaleString()),
        },
      },
      series,
    };

    console.log('图表配置:', option);
    chartInstance.setOption(option, true);
    chartInstance.resize();
  } catch (e: any) {
    chartNoData.value = true;
    message.error('查询失败: ' + (e.message || e));
  }
};
</script>

<style scoped>
/* 顶部外框 */
.public-wrapper {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 顶部栏 */
.top-bar {
  display: flex;
  align-items: center;
  width: 100%;
}

.game-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo {
  width: 70px;
  height: 70px;
  border-radius: 10px;
}

.game-subtitle {
  font-size: 14px;
  color: gray;
  margin: 0;
}

.chart-container {
  border: 5px;
  margin: 10px 10px;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  /* 设置圆角 */
  overflow: hidden;
  /* 确保内容不会溢出圆角 */
  border-radius: 8px;
  box-shadow: 0px 0px 0px rgba(77, 85, 117, 0.05), 0px 3px 7px rgba(77, 85, 117, 0.05), 0px 5px 14px rgba(77, 85, 117, 0.04),
    0px 13px 18px rgba(77, 85, 117, 0.03), 0px 20px 20px rgba(77, 85, 117, 0.01), 0px 35px 30px rgba(77, 85, 117, 0);
}

.chart-container1 {
  display: flex;
  gap: 10px;
  align-items: center;
}

.chart-containertitle {
  border: 3px solid #0893cf;
  border-top: none;
  border-right: none;
  border-bottom: none;
  padding-left: 10px;
  margin-bottom: 20px;
  font-size: medium;
}

.custom-button {
  border: 2px solid #ccc;
  /* 未选中时的灰色边框 */
  color: #636161;
  /* 未选中时的字体颜色 */
  transition: border-color 0.3s, color 0.3s;
  /* 添加过渡效果 */
  margin-bottom: 20px;
}

.custom-button.selected {
  border-color: #0893cf;
  /* 选中时的边框颜色 */
  color: #0893cf;
  /* 选中时的字体颜色 */
}

/* 图标 */
.modal-content {
  padding: 0 20px;
  /* 左右 20px 的间距 */
}

.logo-container {
  position: relative;
  padding-left: 10px;
}

.delete-button {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 20px;
  height: 20px;
  background-color: #ccc;
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 16px;
  cursor: pointer;
}

.delete-button:hover {
  background-color: #e6a3a3;
  /* 鼠标悬停时的背景色 */
  border-color: #d47777;
  /* 鼠标悬停时的边框颜色 */
  color: #999;
  /* 鼠标悬停时的文字颜色 */
}

.add-button {
  width: 60px;
  height: 60px;
  margin-bottom: 35px;
  margin-left: 10px;
  background-color: white;
  border: 2px solid #ccc;
  border-radius: 8px;
  /*圆角大小 */
  font-size: 24px;
  color: #ccc;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 确保加号居中 */
}

.button-label {
  position: absolute;
  /* 绝对定位 */
  margin-top: 75px;
  /* 从按钮底部开始 */
  margin-left: 8px;
  /* 调整文字与按钮的间距 */
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  /* 防止文字换行 */
}
.no-data-container {
  position: absolute;
  left: 0;
  top: 0;
  width: 1000px;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: #ff4d4f;
  font-weight: bold;
  background: white;
  z-index: 10;
}
.add-button-wrapper {
  position: relative;
  /* 使子元素的定位基于此容器 */
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;

  margin-left: 10px;
}

.add-button:hover {
  background-color: #f0f0f0;
  /* 鼠标悬停时的背景色 */
  border-color: #b6b2b2;
  /* 鼠标悬停时的边框颜色 */
  color: #999;
  /* 鼠标悬停时的文字颜色 */
}

.game-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.game-details {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.search-results {
  margin-top: 20px;
}

.search-result-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 10px;
  background-color: #f9f9f9;
  cursor: pointer;
}

.game-image {
  width: 40px;
  height: auto;
}

.game-label {
  font-size: 14px;
  color: #333;
}

.game-title {
  margin: 5px 0;
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
  text-align: center;
}
</style>
