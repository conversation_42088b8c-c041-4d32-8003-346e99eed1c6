<template>

    <div class="chart-container">
        <!-- 顶部栏 -->
        <div class="top-bar">

          <div v-for="(game, index) in games" :key="game.value" class="game-details">
            <div class="logo-container">
              <img :src="game.icon" :alt="game.label" class="logo" />
              <button class="delete-button" @click="deleteGame(index)">-</button>
            </div>
            <p class="game-title" :title="game.label">{{ game.label && game.label.length > 15 ? game.label.slice(0, 15) + '...' : game.label }}</p>
          </div>

            <!-- 加号按钮 -->
            <div class="add-button-wrapper" @click="showModal">
                <button class="add-button">+</button>
                <span class="button-label">添加游戏</span>
            </div>
        </div>

        <!-- 游戏添加弹窗 -->
        <a-modal v-model:visible="isModalVisible" title="添加游戏" @ok="handleOk" @cancel="handleCancel"   :bodyStyle="{ minHeight: '350px' }">
          <a-select
            v-model:value="selectedGames"
            mode="multiple"
            :options="gameOptions"
            :field-names="{ label: 'label', value: 'value' }"
            placeholder="请选择要添加的游戏"
            style="width: 90%;margin: 24px 2% 0 10px;"
            :option-label-prop="'label'"
            :dropdown-render="dropdownRender"
            :filter-option="false"
            :show-search="true"
            :loading="gameLoading"
            :pagination="false"
            @search="onGameSearch"
            @popupScroll="onGameScroll"
          >
            <template #option="{ label, icon }">
              <div style="display: flex; align-items: center;">
                <img :src="icon" alt="" style="width: 24px; height: 24px; margin-right: 8px;" />
                <span>{{ label }}</span>
              </div>
            </template>
          </a-select>
          <div v-if="gameLoading" style="text-align:center;margin-top:10px;">
            <a-spin />
          </div>
        </a-modal>

    <div class="chart-container">
        <div class="chart-containertitle">
            下载数据量分析
        </div>
        <div class="chart-container1">
            <!--日期选择-->
            <a-space direction="vertical" :size="12" style="margin-left: 2%;">
                <a-range-picker :presets="rangePresets" @change="onRangeChange" v-model:value="selectedRange" />
            </a-space>

            <!--地区选择-->
            <a-select
              v-model:value="selectedCountry"
              mode="multiple"
              allowClear
              placeholder="请选择国家"
              style="width: 15vw;"
              :max-tag-count="1"
              :max-tag-placeholder="maxTagPlaceholder"
              @change="handleCountryChange"
            >
              <a-select-option value="all">选择全部</a-select-option>
              <a-select-option v-for="country in countries" :key="country.value" :value="country.value">
                {{ country.label }}
              </a-select-option>
            </a-select>

            <!--平台选择-->
            <a-select
              v-model:value="selectedDevice"
              mode="multiple"
              allowClear
              placeholder="请选择平台"
              style="width: 15vw;"
              :max-tag-count="1"
              :max-tag-placeholder="maxTagPlaceholder"
              @change="handleDeviceChange"
            >
              <a-select-option value="all">选择全部</a-select-option>
              <a-select-option v-for="device in devices" :key="device.value" :value="device.value">
                {{ device.label }}
              </a-select-option>

            </a-select>
            <!-- 查询按钮 -->
            <a-button type="primary" @click="handleQuery" style="margin-left: 20px;">查询</a-button>

            </div>
        <div style="position:relative;">
        <div ref="chartRef" style="width: 1000px; height: 500px; margin: auto;"></div>
        <div
            v-if="chartNoData"
            style="position:absolute;left:0;top:0;width:1000px;height:500px;display:flex;align-items:center;justify-content:center;font-size:28px;color:#ff4d4f;font-weight:bold;z-index:10;background:rgba(255,255,255,0.85);">
            当前筛选条件下无数据
        </div>
        <div
          v-if="!hasQueried"
          style="position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:1000px;height:500px;display:flex;flex-direction:column;align-items:center;justify-content:center;z-index:10;background:rgba(255,255,255,0.85);">
          <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
          <div style="font-size: 20px; color: #666; margin-bottom: 10px;">请选择游戏并点击查询</div>
          <div style="font-size: 14px; color: #999;">最多可选择5款游戏进行比较</div>
        </div>
    </div>
    </div>

    <div class="chart-container2">
        <div class="chart-containertitle table-section">下载来源分析</div>
        <!-- 表格 -->
        <a-table
          :columns="columns"
          :data-source="tableData"
          :pagination="false"
          :bordered="true"
          size="middle"
          
          class="custom-table"
          :scroll="{ x: 'max-content' }"
        >
          <template #bodyCell="{ column, record }">
            <!-- Application Column with avatar and text -->
            <template v-if="column.key === 'app'">
              <div style="display: flex; align-items: center;">
                <a-avatar :src="record.iconUrl" :size="48" style="margin-right: 12px;" />
                <div>
                  <div style="font-weight: bold; line-height: 1.2;">{{ record.nameZh }}</div>
                  <div style="font-weight: lighter; font-size: 12px; line-height: 1.2;">{{ record.publisher_name }}</div>
                </div>
              </div>
            </template>

            <!-- Percentage columns with progress bars -->
            <a-progress 
              v-if="column.key === 'downloadTimeRate'" 
              :percent="Number((record.downloadTimeRate * 100).toFixed(2))"
              size="small"
              :stroke-color="{
                '0%': '#108ee9',
                '100%': '#87d068',
              }"
              :show-info="false"
            />
            <a-progress 
              v-if="column.key === 'incomeRate'" 
              :percent="Number((record.incomeRate * 100).toFixed(2))"
              size="small"
              :stroke-color="{
                '0%': '#108ee9',
                '100%': '#87d068',
              }"
              :show-info="false"
            />
          </template>
          <template #emptyText>
          <div class="empty-data-container">
            <i class="empty-icon">🔍</i>
            <p>没有找到匹配的数据</p>
            <p class="empty-data-tip">请尝试调整筛选条件后再次查询</p>
          </div>
        </template>
        </a-table>

    </div>
    </div>

</template>
<script lang="ts" name="basic-table-demo1" setup>
import { ref, onMounted, watch, h } from 'vue';
import * as echarts from 'echarts';
import dayjs, { Dayjs } from 'dayjs';
import type { TreeSelectProps, } from 'ant-design-vue';
import { TreeSelect } from 'ant-design-vue';
import type { CascaderProps } from 'ant-design-vue';
import mihayoImg from '@/views/release-scan/download-analysis/image/mihayo.png';
import { BasicTable, useTable } from '/@/components/Table';
import { message } from 'ant-design-vue';
import { defHttp } from '/@/utils/http/axios';
import { findGamesByPrefixApi } from '@/api/public-opinion-monitoring';
import { 
  getAllDeviceApi,
  // downloadVolumeApi,
  gameDimensionApi,
  // getDownloadCompareRankApi,
  getAllCountryApi,

} from '@/api/release-scan/download-analysis'

const chartNoData = ref(false);
const isModalVisible = ref(false);
const selectedGames = ref<string[]>([]);
const gameOptions = ref<{ label: string; value: string; icon: string; gameId:string }[]>([]);
const games = ref<{ label: string; value: string; icon: string;gameId:string }[]>([]);
const gameLoading = ref(false);
const gamePage = ref(1);
const gamePageSize = 20;
const gameTotal = ref(0);
const gameSearchKeyword = ref('');
const selectedDevice = ref<string[]>([]);
const selectedCountry = ref<string[]>([]);
const devices = ref<{ value: string; label: string }[]>([]);

// 获取输入框设备类型
const fetchAllDevice = async () => {
  try {
  const res = await getAllDeviceApi();
  // 处理返回值，将apple转换为App Store，并添加"选择全部"选项
  devices.value = (res || []).map((item: any) => ({
    value: item.value,
    label: item.value,
  }));
  } catch (e) {
  devices.value = [];
  }
};

// 获取输入框国家类型
const fetchCountriesGenres = async () => {
  try {
    const res = await getAllCountryApi()
    // 下拉选项保持原样
    countries.value = (res || []).map((item: any) => ({
      value: item.value,
      label: item.value,
    }))
    
  } catch (e) {
    countries.value = []
  }
}

const selectedRange = ref<RangeValue | null>([dayjs().add(-1, 'year'), dayjs()]);
// 使用 useTable 创建表格配置
const [registerTable,{setTableData}] = useTable({
    columns: [
        {
            title: '排名',
            dataIndex: 'ranking',
            key: 'ranking',
            width: 80,
            align: 'center',
        },
        {
            title: '应用',
            dataIndex: 'nameZh',
            key: 'nameZh',
            width: 200,
        },
        {
    title: '商店渠道',
    dataIndex: 'platformId',
    key: 'platformId',
    width: 200,
    customRender: ({ text }) => text === 'apple' ? 'App Store' : text
},
        {
            title: '总下载量',
            dataIndex: 'downloadTimes',
            key: 'downloadTimes',
            width: 120,
            align: 'center',
        },
        {
            title: '自然下载量',
            dataIndex: 'downloadTimes',
            key: 'downloadTimes',
            width: 120,
            align: 'center',
        },
        {
            title: '占总下载量%',
            dataIndex: 'downloadTimeRate',
            key: 'downloadTimeRate',
            width: 150,
        },
        {
            title: '付费下载量',
            dataIndex: 'income',
            key: 'income',
            width: 120,
            align: 'center',
        },
        {
            title: '占总下载量%',
            dataIndex: 'incomeRate',
            key: 'incomeRate',
            width: 150,
        },
    ],
    dataSource: [], // 初始为空
});


const searchQuery = ref('');


// 假数据（模拟搜索结果）
const fakeSearchResults = ref([
    { image: './image/game1.png', label: '原神' },
    { image: './image/game2.png', label: '明日方舟' },
    { image: './image/game3.png', label: '王者荣耀' },
]);

const hasQueried = ref(false);

// 下载量分析数据
const handleQuery = async () => {
  if (!games.value.length || !selectedRange.value) {
    message.warning('请完整选择日期、国家、平台和游戏');
    return;
  }
  hasQueried.value = true;
  try {
    const params = {
      countryNames: selectedCountry.value || null,
      platformNames: selectedDevice.value || null,
      appIdList: games.value.map(g => g.gameId),
      startTime: selectedRange.value[0].format('YYYY-MM-DD'),
      endTime: selectedRange.value[1].format('YYYY-MM-DD'),
      granularity: 'month' // 固定为月粒度
    };
    console.log('获取下载数据量分析发送参数：',params)

    const res = await gameDimensionApi(params);

    console.log('获取下载数据量分析返回数据：',res)

    if (!res || !Array.isArray(res) || res.length === 0) {
      chartNoData.value = true;
      if (chartInstance) chartInstance.clear();
      return;
    }

    // 处理数据：提取所有月份并排序
    const allMonths = new Set<string>();
    const gameDataMap = new Map<string, { [month: string]: number }>();

    res.forEach(game => {
      const gameId = game.appId;
      const downloadsMap: { [month: string]: number } = {};
      
      game.period.forEach(period => {
        // 转换月份格式: 2025-01 -> Jan 2025
        const monthStr = formatMonthLabel(period.time);
        allMonths.add(monthStr);
        downloadsMap[monthStr] = period.downloads;
      });
      
      gameDataMap.set(gameId, downloadsMap);
      console.log('映射后数据：',gameDataMap)
    });
    
    // 排序月份
    const sortedMonths = Array.from(allMonths).sort((a, b) => {
      return new Date(a.split(' ')[1] + '-' + a.split(' ')[0] + '-01').getTime() - 
             new Date(b.split(' ')[1] + '-' + b.split(' ')[0] + '-01').getTime();
    });

    // 准备系列数据
    // 把 seriesData 的声明改成这样
    const seriesData: any[] = [];
    const colors = ['#0893CF', '#FF6B6B', '#4ECDC4', '#FFA07A', '#7B68EE']; // 不同游戏的颜色
    
    games.value.forEach((game, index) => {
      const gameId = game.gameId;
      const gameData = gameDataMap.get(gameId) || {};
      
      const data = sortedMonths.map(month => {
        return gameData[month] || 0;
      });
      
      console.log('用于画柱状图数据：',data)
      seriesData.push({
        name: game.label,
        type: 'bar',
        data: data,
        barWidth: 20,
        itemStyle: { color: colors[index % colors.length] }
      });
    });
    
    // 更新图表
    if (chartInstance) {
      chartNoData.value = false;
      chartInstance.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' }
        },
        legend: {
          data: games.value.map(g => g.label),
          bottom: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: sortedMonths,
          axisLabel: {
            interval: 0,
            formatter: (value: string) => value
          }
        },
        yAxis: {
          type: 'value',
          name: '下载量'
        },
        series: seriesData
      });
    }
    
  } catch (e) {
    console.error('查询失败', e);
    message.error('查询失败');
    chartNoData.value = true;
    if (chartInstance) chartInstance.clear();
  }
};

// 新增月份格式化函数
const formatMonthLabel = (monthStr: string) => {
  const [year, month] = monthStr.split('-');
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  return `${monthNames[parseInt(month) - 1]} ${year}`;
};

// 显示弹窗
const showModal = () => {
    isModalVisible.value = true;
      // 弹窗打开时拉取全部游戏
    gameOptions.value = [];
    gamePage.value = 1;
    fetchGameOptions();
};

const fetchGameOptions = async (append = false) => {
  gameLoading.value = true;
  try {
    const res = await findGamesByPrefixApi({
      prefix: gameSearchKeyword.value || '',
    });
    // 前缀树API返回带records字段的对象
    const list = res?.records || [];
    gameTotal.value = list.length;
    const newOptions = list.map((item: any) => ({
      label: item.nameZh,
      value: item.id,
      icon: item.iconUrl,
      gameId: item.id // 使用id作为gameId
    }));

    // 去重：使用gameId作为唯一标识符
    const uniqueOptions = newOptions.filter((option, index, self) =>
      index === self.findIndex((t) => t.gameId === option.gameId)
    );

    // 前缀树API返回所有匹配结果，直接替换
    gameOptions.value = uniqueOptions;
  } catch (e) {
    message.error('获取游戏列表失败');
  } finally {
    gameLoading.value = false;
  }
};

const onGameSearch = (val: string) => {
  gameSearchKeyword.value = val;
  fetchGameOptions();
};

const onGameScroll = (e: Event) => {
  // 前缀树API不支持分页，所以不需要滚动加载更多
  // 保留函数以避免模板报错，但不执行任何操作
};

const maxGames = 5; // 最大游戏数量限制

// 修改handleOk函数
const handleOk = () => {
  if (selectedGames.value.length + games.value.length > maxGames) {
    message.warning(`最多只能添加${maxGames}款游戏进行比较`);
    return;
  }
  
  // 获取选中的游戏信息，并确保不重复
  const selectedGameInfo = gameOptions.value
    .filter(option => selectedGames.value.includes(option.value))
    .filter((option, index, self) => 
      index === self.findIndex((t) => t.gameId === option.gameId)
    );
  
  console.log('当前已选游戏:', games.value.map(g => ({ label: g.label, gameId: g.gameId })));
  console.log('新选择的游戏:', selectedGameInfo.map(g => ({ label: g.label, gameId: g.gameId })));
  
  // 使用gameId进行去重
  const newGames = selectedGameInfo.filter(newGame => 
    !games.value.some(existingGame => existingGame.gameId === newGame.gameId)
  );
  
  console.log('去重后要添加的游戏:', newGames.map(g => ({ label: g.label, gameId: g.gameId })));
  
  if (newGames.length === 0) {
    message.info('所选游戏已全部添加');
    selectedGames.value = [];
    isModalVisible.value = false;
    return;
  }
  
  // 添加到games数组
  games.value = [...games.value, ...newGames];
  
  // 清空选择
  selectedGames.value = [];
  isModalVisible.value = false;
};

const handleCancel = () => {
  isModalVisible.value = false;
  selectedGames.value = [];
};

const deleteGame = (index: number) => {
  games.value.splice(index, 1);
};

const dropdownRender = ({ menuNode }) => menuNode;


// 统计图表部分
const chartRef = ref<HTMLElement | null>(null);
let chartInstance: echarts.ECharts | null = null;
// const chart = echarts.init(document.getElementById('chart'));
// chart.setOption(option);
// 图表配置

// 在组件挂载后初始化图表
onMounted(() => {
  fetchAllDevice();
  fetchCountriesGenres();
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value);
  }
});
//时间选择框
type RangeValue = [Dayjs, Dayjs];
const rangePresets = ref([
    { label: '当天', value: [dayjs().add(-1, 'd'), dayjs()] },
    { label: '最近三天', value: [dayjs().add(-3, 'd'), dayjs()] },
    { label: '最近一周', value: [dayjs().add(-7, 'd'), dayjs()] },
    { label: '最近一个月', value: [dayjs().add(-1, 'month'), dayjs()] },
    { label: '最近三个月', value: [dayjs().add(-3, 'month'), dayjs()] },
    { label: '最近六个月', value: [dayjs().add(-6, 'month'), dayjs()] },
    { label: '最近一年', value: [dayjs().add(-1, 'year'), dayjs()] },
    { label: '最近两年', value: [dayjs().add(-2, 'year'), dayjs()] },
    { label: '最近三年', value: [dayjs().add(-3, 'year'), dayjs()] },
]);
const onRangeChange = (dates: RangeValue, dateStrings: string[]) => {
    if (dates) {
        console.log('From: ', dates[0], ', to: ', dates[1]);
        console.log('From: ', dateStrings[0], ', to: ', dateStrings[1]);
    } else {
        console.log('Clear');
    }
};

//国家选择
const countries = ref<{ value: string; label: string }[]>([]);
const value1 = ref<string[]>([]);
//平台选择
const value2 = ref<string[]>([]);
watch(value2, () => {
    console.log(value2.value);
});

//百分比表格

// 在获取数据的函数中添加数量限制
// const fetchData = async () => {
//   try {
//     loading.value = true;
//     const response = await defHttp.get({
//       url: '/your-api-endpoint',
//       params: {
//         // 您的参数
//       }
//     });
//     // 限制游戏数量为5个
//     gameList.value = (response.data || []).slice(0, maxGames);
//   } catch (error) {
//     console.error('获取数据失败:', error);
//     gameList.value = [];
//   } finally {
//     loading.value = false;
//   }
// };

const columns = [
  {
    title: '排名',
    dataIndex: 'ranking',
    key: 'ranking',
    width: 80,
    align: 'center',
  },
  {
    title: '应用',
    dataIndex: 'app',
    key: 'app',
    width: 300,
  },
  {
    title: '商店渠道',
    dataIndex: 'platformId',
    key: 'platformId',
    width: 150,
    customRender: ({ text }) => text === 'apple' ? 'App Store' : text
  },
  {
    title: '总下载量',
    dataIndex: 'downloadTimes',
    key: 'downloadTimes',
    width: 120,
    align: 'center',
  },
  {
    title: '自然下载量',
    dataIndex: 'downloadTimes',
    key: 'downloadTimes',
    width: 120,
    align: 'center',
  },
  {
    title: '占总下载量%',
    dataIndex: 'downloadTimeRate',
    key: 'downloadTimeRate',
    width: 150,
  },
  {
    title: '付费下载量',
    dataIndex: 'income',
    key: 'income',
    width: 120,
    align: 'center',
  },
  {
    title: '占总下载量%',
    dataIndex: 'incomeRate',
    key: 'incomeRate',
    width: 150,
  },
];

const tableData = ref<any[]>([]);

// 添加 maxTagPlaceholder 函数
const maxTagPlaceholder = (omittedValues: any[]) => {
  return h('span', { style: { color: '#666' } }, `+${omittedValues.length}...`);
};

// 添加选择变化处理函数
const handleCountryChange = (value: string[]) => {
  if (value.includes('all')) {
    // 如果只选了all，表示全选；如果all和其他一起选，表示清空
    if (value.length === 1) {
      selectedCountry.value = countries.value.map(c => c.value);
    } else {
      selectedCountry.value = [];
    }
  } else {
    selectedCountry.value = value;
  }
};

const handleDeviceChange = (value: string[]) => {
  if (value.includes('all')) {
    if (value.length === 1) {
      selectedDevice.value = devices.value.map(d => d.value);
    } else {
      selectedDevice.value = [];
    }
  } else {
    selectedDevice.value = value;
  }
};

</script>
<style scoped>
/* 顶部外框 */
.public-wrapper {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 顶部栏 */
.top-bar {
    display: flex;
    align-items: center;
    width: 100%;
}

.game-info {
    display: flex;
    flex-direction: column;
    /* 垂直排列 */
    align-items: center;
    gap: 5px;
    margin-left: 10px;
    margin-right: 10px;
}

.logo {
    width: 70px;
    height: 70px;
    border-radius: 10px;
}

.game-details {
    display: flex;
    flex-direction: column;

    margin-left: 10px;

}

.game-title {
    margin: 5px 0;
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100px;
    text-align: center;
}

.game-subtitle {
    font-size: 14px;
    color: gray;
    margin: 0;
}







.chart-container {

    border: 5px;
    margin: 10px 10px;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    /* 设置圆角 */
    overflow: hidden;
    /* 确保内容不会溢出圆角 */
    border-radius: 8px;
    box-shadow:
        0px 0px 0px rgba(77, 85, 117, 0.05),
        0px 3px 7px rgba(77, 85, 117, 0.05),
        0px 5px 14px rgba(77, 85, 117, 0.04),
        0px 13px 18px rgba(77, 85, 117, 0.03),
        0px 20px 20px rgba(77, 85, 117, 0.01),
        0px 35px 30px rgba(77, 85, 117, 0);

}

.chart-container1 {
    display: flex;
    gap: 10px;
    align-items: center;
}
.chart-container2 {
  border: 5px;
  margin: 10px 10px;
  height: auto;
  min-height: 400px;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  overflow: visible;
  box-shadow:
    0px 0px 0px rgba(77, 85, 117, 0.05),
    0px 3px 7px rgba(77, 85, 117, 0.05),
    0px 5px 14px rgba(77, 85, 117, 0.04),
    0px 13px 18px rgba(77, 85, 117, 0.03),
    0px 20px 20px rgba(77, 85, 117, 0.01),
    0px 35px 30px rgba(77, 85, 117, 0);
}
.chart-containertitle {
    border: 3px solid #0893cf;
    border-top: none;
    border-right: none;
    border-bottom: none;
    padding-left: 10px;
    margin-bottom: 20px;
    font-size: medium;
}

.modal-content {
    padding: 0 20px;
    /* 左右 20px 的间距 */
}




.delete-button {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 20px;
    height: 20px;
    background-color: #ccc;
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 16px;
    cursor: pointer;
}


.delete-button:hover {
    background-color: #e6a3a3;
    /* 鼠标悬停时的背景色 */
    border-color: #d47777;
    /* 鼠标悬停时的边框颜色 */
    color: #999;
    /* 鼠标悬停时的文字颜色 */
}

/* .add-button {
    width: 60px;
    height: 60px;
    background-color: white;
    border: 2px solid #ccc;
    border-radius: 8px;
    font-size: 24px;
    color: #ccc;
    cursor: pointer;
} */


.add-button {
    width: 60px;
    height: 60px;
    margin-bottom: 35px;
    margin-left: 10px;
    background-color: white;
    border: 2px solid #ccc;
    border-radius: 8px;
    /*圆角大小 */
    font-size: 24px;
    color: #ccc;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    /* 确保加号居中 */
}

.button-label {
    position: absolute;
    /* 绝对定位 */
    margin-top: 75px;
    /* 从按钮底部开始 */
    margin-left: 8px;
    /* 调整文字与按钮的间距 */
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    /* 防止文字换行 */
}

.add-button-wrapper {
    position: relative;
    /* 使子元素的定位基于此容器 */
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;

    margin-left: 10px;
}

.add-button:hover {
    background-color: #f0f0f0;
    /* 鼠标悬停时的背景色 */
    border-color: #b6b2b2;
    /* 鼠标悬停时的边框颜色 */
    color: #999;
    /* 鼠标悬停时的文字颜色 */
}

.game-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
}

.game-details {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.logo-container {
    position: relative;
}




.search-results {
    margin-top: 20px;
}

.search-result-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 10px;
    background-color: #f9f9f9;
    cursor: pointer;
}

.game-image {
    width: 40px;
    height: auto;
}

.game-label {
    font-size: 14px;
    color: #333;
}
/* 新增 */
.chart-container.table-section {
  max-height: none;
  overflow: visible;
}

/* 确保表格内容完整显示 */
:deep(.ant-table-wrapper) {
  overflow: visible;
}

:deep(.ant-table) {
  overflow: visible;
}

:deep(.ant-table-body) {
  overflow: visible !important;
}

.game-info {
  .game-name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
    line-height: 1.2;
  }
  
  .game-developer {
    font-size: 14px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
    line-height: 1.2;
  }
}

:deep(.ant-table) {
  background: #fff;
  border-radius: 8px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 500;
  color: #333;
  padding: 12px 16px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
}

:deep(.ant-progress-bg) {
  height: 8px !important;
}

:deep(.ant-progress-inner) {
  background-color: #f5f5f5;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

:deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-table-thead > tr > th) {
  border-bottom: 1px solid #f0f0f0;
}
.custom-table {
  width: 100%;
  font-size: 14px;
}

/* 表头样式 */
:deep(.custom-table .ant-table-thead > tr > th) {
  background-color: #c2e8f8;
  color: #333;
  font-weight: bold;
  text-align: center;
  padding: 12px 16px;
}

/* 表格单元格样式 */
:deep(.custom-table .ant-table-tbody > tr > td) {
  padding: 12px 16px;
  text-align: center;
  color: #666;
  vertical-align: middle;
  border-bottom: 1px solid #eee;
}

/* 斑马纹样式 */
:deep(.custom-table .ant-table-tbody > tr:nth-child(odd)) {
  background-color: #ffffff;
}
:deep(.custom-table .ant-table-tbody > tr:nth-child(even)) {
  background-color: #dcf2fb;
}

/* 悬停样式 */
:deep(.custom-table .ant-table-tbody > tr:hover > td) {
  background-color: #e6f7ff;
}

.empty-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.empty-icon {
  font-size: 32px;
  color: #ccc;
  margin-bottom: 10px;
}

.empty-data-container p {
  margin: 0;
  font-size: 16px;
  color: #666;
}

.empty-data-tip {
  font-size: 14px;
  color: #999;
}
</style>
