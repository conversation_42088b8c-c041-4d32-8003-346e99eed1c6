<template>
  <div class="page-container">
    <game-title @game-info-update="handleGameInfoUpdate" @plan-update="handlePlanUpdate" />
    <!-- <game-title @game-info-update="handleGameInfoUpdate" @plan-update="handlePlanUpdate" /> -->

    <!-- 舆情数据 -->
    <div class="today-public-opinion">
      <div class="today-opinion-item">
        <div class="today-opinion-box">
          <img :src="logoUrl1" alt="崩坏3" class="today-opinion-image" />
          <div class="today-opinion-text">
            <h3>今日舆情</h3>
            <p>{{ publicOpinionData.publicOpinion }}个</p>
          </div>
        </div>
      </div>
      <div class="today-opinion-item">
        <div class="today-opinion-box">
          <img :src="logoUrl2" alt="崩坏3" class="today-opinion-image" />
          <div class="today-opinion-text">
            <h3>参与讨论人数</h3>
            <p>{{ publicOpinionData.participantsNumber }}个</p>
          </div>
        </div>
      </div>
      <div class="today-opinion-item">
        <div class="today-opinion-box">
          <img :src="logoUrl3" alt="崩坏3" class="today-opinion-image" />
          <div class="today-opinion-text">
            <h3>正面舆情</h3>
            <p>{{ publicOpinionData.sumPositive }}个</p>
          </div>
          <!-- 省略号按钮 -->
          <a-button type="link" class="ellipsis-button" @click="goToPositiveOpinionPage">
            <img :src="moreIcon" alt="更多" class="more-icon" />
          </a-button>
        </div>
      </div>

      <div class="today-opinion-item">
        <div class="today-opinion-box">
          <img :src="logoUrl4" alt="崩坏3" class="today-opinion-image" />
          <div class="today-opinion-text">
            <h3>负面舆情</h3>
            <p>{{ publicOpinionData.sumNegative }}个</p>
          </div>
          <!-- 省略号按钮 -->
          <a-button type="link" class="ellipsis-button" @click="goToNegativeOpinionPage">
            <img :src="moreIcon" alt="更多" class="more-icon" />
          </a-button>
        </div>
      </div>
    </div>

    <!-- 左右两列布局 -->
    <div class="main-content">
      <!-- 左半边：舆情渠道和今日热点/热门话题（占 2/3） -->
      <div class="left-column" ref="leftColumnRef">
        <!-- 舆情渠道 -->
        <div class="opinion-source">
          <div class="header-with-more">
            <h2>舆情渠道</h2>
            <!--            <a-button type="link" class="more-button" @click="viewMore('source')">查看更多</a-button>-->
          </div>
          <!--          <div id="sourceChart" ref="soureChartRef" style="width: 100%; height: 400px"></div>-->
          <nightingale-pie-chart :chart-data="sourceList" width="100%" height="350px" />
        </div>
        <!-- 今日热点 -->
        <div class="hot-news">
          <div class="header-with-more">
            <h2>今日热点</h2>
            <!--            <a-button type="link" class="more-button" @click="viewMore('hotNews')">查看更多</a-button>-->
          </div>
          <div class="hot-layout">
            <!-- 左半边：表格（排名、名称、热度） -->
            <div class="hot-table-container">
              <table class="hot-table">
                <thead>
                  <tr>
                    <th>排名</th>
                    <th>名称</th>
                    <th>热度</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in newsList" :key="index">
                    <td>{{ index + 1 }}</td>
                    <td>{{ item.title }}</td>
                    <td>{{ item.views }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <!-- 右半边：词云图 -->
            <div class="word-cloud-container">
              <!--              <div id="newsWordCloudChart" style="width: 100%; height: 300px"></div>-->
              <word-cloud-chart :chart-data="newsChartList" />
            </div>
          </div>
        </div>

        <!-- 今日热门话题 -->
        <div class="hot-topic">
          <div class="header-with-more">
            <h2>今日热门话题</h2>
            <!--            <a-button type="link" class="more-button" @click="viewMore('hotTopic')">查看更多</a-button>-->
          </div>
          <div class="hot-layout">
            <!-- 左半边：表格（排名、名称、热度） -->
            <div class="hot-table-container">
              <table class="hot-table">
                <thead>
                  <tr>
                    <th>序号</th>
                    <th>话题</th>
                    <th>负面占比</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in newsList_topic" :key="index">
                    <td>{{ index + 1 }}</td>
                    <td>{{ item.title }}</td>
                    <td>{{ item.views }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <!-- 右半边：词云图 -->
            <div class="word-cloud-container">
              <!--              <div id="topicWordCloudChart" style="width: 100%; height: 300px"></div>-->
              <word-cloud-chart :chart-data="newsChartList_topic" />
            </div>
          </div>
        </div>
      </div>

      <!-- 右半边：热帖列表（占 1/3） -->
      <div class="right-column" ref="rightColumnRef">
        <div class="header-with-more">
          <div class="title-container">
            <h2>热帖</h2>
            <div v-if="showRecentPostsTip" class="recent-posts-tip">
              <div class="tip-content">
                <Icon icon="ant-design:info-circle-outlined" class="tip-icon" />
                <span class="tip-text">今日无热帖，为您展示最近热帖</span>
              </div>
            </div>
          </div>
          <div class="scroll-indicator" v-if="hasMore">
            <Icon icon="ant-design:arrow-down-outlined" class="scroll-icon" />
            <span>向下滚动查看更多</span>
          </div>
        </div>
        <div class="hot-posts-list" ref="hotPostsListRef" @scroll="handleScroll">
          <div class="hot-post-item" v-for="(post, index) in hotPostsData" :key="post.id">
            <span class="post-rank">{{ index + 1 }}</span>
            <div class="post-content">
              <a :href="post.postUrl" target="_blank" class="post-title-link">
                <span class="post-title">{{ post.title }}</span>
              </a>
              <span class="post-date">{{ post.date }}</span>
            </div>
            <div class="post-views-container">
              <img :src="retieIcon" alt="热帖图标" class="post-views-icon" />
              <span class="post-views">{{ post.views }}</span>
            </div>
          </div>
          <!-- 加载状态提示 -->
          <div class="loading-container" v-if="loadingMore">
            <div class="loading-more">
              <img :src="loadingLogo" alt="加载中" class="loading-logo-img" />
              <span>加载中...</span>
            </div>
          </div>
          <div class="no-more-data" v-if="!hasMore && hotPostsData.length > 0">
            <span>没有更多数据了</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '/@/store/modules/user';
  import { useGameStore } from '/@/store/modules/gameStore';
  import * as echarts from 'echarts';
  import 'echarts-wordcloud';
  import { Icon } from '/@/components/Icon';
  import logoUrl from '@/assets/images/OIP.jpg';
  import logoUrl1 from '@/assets/images/jinri.png';
  import logoUrl2 from '@/assets/images/taolun.png';
  import logoUrl3 from '@/assets/images/py.png';
  import logoUrl4 from '@/assets/images/ny.png';
  import retieIcon from '@/assets/images/retie.png';
  import moreIcon from '@/assets/images/moreee.png';
  import loadingLogo from '@/assets/images/logo.png';
  import gameTitle from '../components/gameTitle.vue';
  import NightingalePieChart from '../components/chart/NightingalePieChart.vue';
  import WordCloudChart from '../components/chart/WordCloudChart.vue';
  import {
    getPublicOpinionDataSourceApi,
    getPublicOpinionDataTodayApi,
    getHotTodayDataApi,
    getHotPostListApi,
    getTrendingTodayApi,
  } from '@/api/public-opinion-monitoring/today';
  import { ThemeEnum } from '@/enums/appEnum';
  import { theme } from 'ant-design-vue';
  import { usePlanStore } from '@/store/modules/planStore';

  const router = useRouter();
  const userStore = useUserStore();
  const gameStore = useGameStore();
  const planStore = usePlanStore();
  //选中游戏Id
  const gameId = gameStore.gameInfo.gameId;
  //获取用户Id
  const userId = userStore.getUserInfo?.id;
  //选中planId
  const selectedPlanId = planStore.selectedPlanId;
  const planId = ref();

  // 舆情渠道数据
  const sourceList = ref([
    // { name: '百度贴吧-王者1', value: 20 },
    // { name: '百度贴吧-王者2', value: 12 },
    // { name: '百度贴吧-王者3', value: 23 },
    // { name: 'TopTop-王者1', value: 22 },
    // { name: 'TopTop-王者2', value: 44 },
    // { name: '华为应用商店-王者1', value: 55 },
    // { name: '华为应用商店-王者2', value: 66 },
  ]);
  // 获取舆情渠道-饼图数据
  const fetchPieData = async (gameId, planId) => {
    // 检查planId是否存在
    if (!planId || planId === '') {
      return;
    }

    try {
      // 获取当前日期
      const now = new Date();
      // 获取当前时间的时间戳（毫秒）
      const localDate = now.getTime(); // 使用当前时间的毫秒级时间戳

      const params = {
        planId,
        userId,
        // gameId,
        localDate,
        // endTime,
      };
      const res = await getPublicOpinionDataSourceApi(params);
      sourceList.value = res.source;

      if (!res) {
        return;
      }
    } catch (error) {
      // 获取饼图数据异常
    }
  };

  // 跳转到正面舆情页面
  const goToPositiveOpinionPage = () => {
    // 从localStorage获取当前planId
    const currentPlanId = localStorage.getItem('currentPlanId') || '';
    router.push({
      name: 'positive-detail',
      query: { planId: currentPlanId },
    }); // 使用路由名称跳转，并传递planId参数
  };

  // 跳转到负面舆情页面
  const goToNegativeOpinionPage = () => {
    // 从localStorage获取当前planId
    const currentPlanId = localStorage.getItem('currentPlanId') || '';
    router.push({
      name: 'negative-detail',
      query: { planId: currentPlanId },
    }); // 使用路由名称跳转，并传递planId参数
  };

  // 热点新闻数据
  const newsList = ref([
    // { title: '玩法', date: '2024/7/3', views: 14534 },
    // { title: '地图', date: '2024/7/3', views: 12334 },
    // { title: '设计', date: '2024/7/3', views: 12312 },
    // { title: '探索', date: '2024/7/3', views: 12114 },
    // { title: '剧情', date: '2024/7/3', views: 11134 },
  ]);
  const newsChartList = ref([]);

  // 热门话题数据
  const newsList_topic = ref([
    // { title: '玩法', date: '2024/7/3', views: '24%' },
    // { title: '角色', date: '2024/7/3', views: '21%' },
    // { title: '任务', date: '2024/7/3', views: '54%' },
    // { title: '攻略', date: '2024/7/3', views: '34%' },
    // { title: '剧情', date: '2024/7/3', views: '29%' },
  ]);
  const newsChartList_topic = ref([]);

  // 添加缺失的变量定义
  const trendingList = ref([]);
  const trendingChartList = ref([]);

  // 热帖数据相关状态
  const hotPostsData = ref([]);
  const currentPage = ref(1);
  const hasMore = ref(true);
  const loadingMore = ref(false);
  const lastPostTime = ref('');

  // 添加ref引用
  const leftColumnRef = ref(null);
  const rightColumnRef = ref(null);
  const hotPostsListRef = ref(null);

  // 动态调整右侧热帖窗口高度以匹配左侧内容
  const adjustRightColumnHeight = () => {
    if (leftColumnRef.value && rightColumnRef.value && hotPostsListRef.value) {
      // 获取左侧内容的实际高度
      const leftColumnHeight = leftColumnRef.value.offsetHeight;

      // 获取右侧容器的padding和标题区域高度
      const rightColumnPadding = 40; // 上下padding各20px
      const headerHeight = 60; // 标题区域大约60px

      // 计算热帖列表应该的高度
      const hotPostsListHeight = leftColumnHeight - rightColumnPadding - headerHeight - 14;

      // 设置最小高度限制，确保有足够空间显示加载动画
      const minHeight = 400; // 调整为400px，约可显示8-10个热帖项目，为加载动画提供充足空间
      const finalHeight = Math.max(hotPostsListHeight, minHeight);

      // 应用高度到热帖列表
      hotPostsListRef.value.style.height = `${finalHeight}px`;
    }
  };

  // 监听窗口大小变化
  const handleResize = () => {
    setTimeout(() => {
      adjustRightColumnHeight();
    }, 100);
  };
  watch(
    () => selectedPlanId,
    (newValue) => {
      handlePlanUpdate({ planId: newPlanId, gameId: gameId });
    }
  );
  watch(
    () => gameId,
    (newValue) => {
      // 游戏切换时清空数据，等待新的planId
      clearAllData();
    }
  );

  // 组件挂载时添加事件监听器
  onMounted(async () => {
    // window.addEventListener('resize', handleResize);
    // // 监听游戏切换事件
    // window.addEventListener('gameIdChanged', (event) => {
    //   const newGameId = event.detail;
    //   // 游戏切换时清空数据，等待新的planId
    //   clearAllData();
    // });
    //
    // // 监听planId变化事件
    // window.addEventListener('planIdChanged', async (event) => {
    //   const newPlanId = event.detail;
    //
    //   if (newPlanId && newPlanId !== '') {
    //     // 有新的planId时，获取对应的游戏ID并刷新数据
    //
    //     if (gameId) {
    //       await handlePlanUpdate({ planId: newPlanId, gameId: gameId });
    //     }
    //   } else {
    //     // planId为空时，清空数据
    //     clearAllData();
    //   }
    // });

    // 备用初始化逻辑：如果game-title组件没有触发事件，手动检查并加载数据
    setTimeout(async () => {
      const currentPlanId = localStorage.getItem('currentPlanId');

      // 如果有planId但页面数据为空，说明可能没有正确初始化
      if (currentPlanId && currentPlanId !== '' && publicOpinionData.value.publicOpinion === 0) {
        console.log('检测到页面数据未初始化，开始手动加载数据...');
        const currentGameId = gameStore.gameInfo?.gameId;
        if (currentGameId) {
          await handlePlanUpdate({ planId: currentPlanId, gameId: currentGameId });
        }
      }
    }, 1000); // 延迟1秒执行，确保game-title组件有足够时间完成初始化
  });
  //
  // // 组件卸载时移除事件监听器
  // onUnmounted(() => {
  //   window.removeEventListener('resize', handleResize);
  //   window.removeEventListener('gameIdChanged', () => {});
  //   window.removeEventListener('planIdChanged', () => {});
  // });

  // 获取热帖数据
  const fetchHotPostData = async (gameId, planId, isLoadMore = false, pagesToLoad = 1) => {
    // 检查planId是否存在
    if (!planId || planId === '') {
      return;
    }

    if (!isLoadMore) {
      hotPostsData.value = [];
      currentPage.value = 1;
      lastPostTime.value = '';
      hasMore.value = true;
    }

    if (!hasMore.value || loadingMore.value) return;

    try {
      loadingMore.value = true;
      // console.log('开始获取热帖数据，游戏ID：', gameId, '方案ID：', planId, '页数：', pagesToLoad);
      const userId = userStore.getUserInfo?.id;
      if (!userId) {
        // console.error('用户ID不存在');
        return;
      }

      // 存储所有格式化后的数据
      let allFormattedData = [];

      // 保存起始页码，避免循环中修改currentPage导致页码跳跃
      const startPage = currentPage.value;
      // 获取当前时间
      const now = new Date();
      // 获取当前时间的时间戳（毫秒）
      const localDate = now.getTime(); // 使用当前时间的毫秒级时间戳

      // 循环加载指定页数的数据
      for (let i = 0; i < pagesToLoad; i++) {
        const pageToLoad = startPage + i;

        const params = {
          planId,
          userId,
          page: pageToLoad, // 使用page参数
          localDate,
        };

        // console.log('热帖请求参数：', params);

        const res = await getHotPostListApi(params);
        console.log(`第${pageToLoad}页热帖数据接口返回：`, res);

        if (!res) {
          // console.error('接口返回数据为空');
          break;
        }

        // 检查数据结构，兼容不同的返回格式
        let resultsData = [];

        if (res.code === 200 && res.result?.results) {
          resultsData = res.result.results;
        } else if (res.results && Array.isArray(res.results)) {
          resultsData = res.results;
        } else {
          // console.error('接口返回数据格式无法解析：', res);
          break;
        }

        // 如果返回的数据为空，表示没有更多数据了
        if (resultsData.length === 0) {
          hasMore.value = false;
          break;
        }

        // 处理返回的数据
        const formattedData = resultsData.map((item) => {
          let title = item.title;
          if (title === '无' || !title) {
            title = item.content.length > 30 ? item.content.substring(0, 30) + '...' : item.content;
          }
          title = title.replace(/\r?\n/g, ' ');

          // 处理日期，将时间戳转换为日期格式
          let formattedDate = '暂无日期';
          if (item.postDate) {
            try {
              // 确保时间戳是数字类型
              const timestamp = parseInt(item.postDate);
              if (!isNaN(timestamp)) {
                const date = new Date(timestamp);
                formattedDate = `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
              }
            } catch (e) {
              // console.error('日期格式化错误:', e);
            }
          }

          return {
            id: Math.random().toString(36).substring(2, 11),
            title: title,
            date: formattedDate,
            views: item.amount.toString(),
            postUrl: item.postUrl || '#', // 添加帖子链接，如果不存在则使用#作为默认值
          };
        });

        // 将当前页的数据添加到总数据中
        allFormattedData = [...allFormattedData, ...formattedData];
      }

      // 更新数据和状态
      if (isLoadMore) {
        hotPostsData.value = [...hotPostsData.value, ...allFormattedData];
      } else {
        hotPostsData.value = allFormattedData;
      }

      // 循环结束后一次性更新页码，设置为下一个要加载的页码
      currentPage.value = startPage + pagesToLoad;
    } catch (error) {
      // console.error('获取热帖数据异常：', error);
    } finally {
      loadingMore.value = false;
    }
  };

  // 处理滚动事件
  const handleScroll = async (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    // 当距离底部100px时触发加载更多
    if (scrollHeight - scrollTop - clientHeight < 100 && !loadingMore.value && hasMore.value) {
      // 从localStorage获取当前planId
      const currentPlanId = localStorage.getItem('currentPlanId');
      if (!currentPlanId) {
        // console.error('未找到planId');
        return;
      }
      // 滚动加载时，每次只加载一页
      await fetchHotPostData(undefined, currentPlanId, true, 1);
      console.log('到我了。。。。。。');
    }
  };

  // 获取今日舆情数据
  const fetchPublicOpinionData = async (gameId, planId) => {
    // 检查planId是否存在
    if (!planId || planId === '') {
      return;
    }

    try {
      const userId = userStore.getUserInfo?.id;
      if (!userId) {
        return;
      }
      // 获取当前时间
      const now = new Date();
      // 获取当前时间的时间戳（毫秒）
      const localDate = now.getTime(); // 使用当前时间的毫秒级时间戳

      const params = {
        planId,
        userId,
        localDate,
      };
      // console.log('今日舆情数据请求参数：', params);
      const res = await getPublicOpinionDataTodayApi(params);
      // console.log('今日舆情数据接口返回：', res);
      publicOpinionData.value = res.results;

      if (!res) {
        // console.error('接口返回数据为空');
        // message.error('获取今日舆情数据失败：接口返回数据为空');
        return;
      }
    } catch (error) {
      // console.error('获取今日舆情数据异常：', error);
    }
  };

  // 获取今日热点数据
  const fetchHotTodayData = async (gameId, planId) => {
    // 检查planId是否存在
    if (!planId || planId === '') {
      return;
    }

    // 获取当前时间
    const now = new Date();
    // 获取当前时间的时间戳（毫秒）
    const localDate = now.getTime(); // 使用当前时间的毫秒级时间戳

    try {
      const params = {
        planId,
        userId,
        localDate,
      };

      // 尝试从API获取数据
      let res;
      try {
        res = await getHotTodayDataApi(params);
      } catch (error) {
        console.log('API调用失败，使用测试数据');
      }

      // 如果API没有返回数据，使用测试数据
      if (!res || !res.results || res.results.length === 0) {
        console.log('使用测试数据进行排序测试');
        // 模拟数据用于测试排序
        res = {
          results: [
            { token: '游戏体验', tokenCount: 50 },
            { token: '画面质量', tokenCount: 120 },
            { token: '角色设计', tokenCount: 30 },
            { token: '游戏难度', tokenCount: 80 },
            { token: '剧情发展', tokenCount: 100 },
            { token: '操作手感', tokenCount: 70 },
            { token: '音效音乐', tokenCount: 40 }
          ]
        };
      }

      // 按热度排序后更新今日热点数据，限制显示5条
      const sortedHotResults = [...res.results].sort((a, b) => {
        const aValue = parseInt(a.tokenCount) || 0;
        const bValue = parseInt(b.tokenCount) || 0;
        return bValue - aValue; // 降序排列，热度高的在前
      });

      newsList.value = sortedHotResults.slice(0, 5).map((item) => ({
        title: item.token,
        views: item.tokenCount.toString(),
      }));
      newsChartList.value = sortedHotResults.slice(0, 30).map((item) => ({
        title: item.token,
        views: item.tokenCount.toString(),
      }));
      // 更新词云图
      // if (newsWordCloudChart) {
      //   newsWordCloudChart.setOption({
      //     series: [
      //       {
      //         data: newsList.value.map((item) => ({
      //           name: item.title,
      //           value: parseInt(item.views) || 1,
      //         })),
      //       },
      //     ],
      //   });
      // }
    } catch (error) {
      // console.error('获取今日热点数据异常：', error);
    }
  };

  // 获取今日热门话题数据
  const fetchTrendingTodayData = async (gameId, planId) => {
    // 检查planId是否存在
    if (!planId || planId === '') {
      return;
    }

    // 获取当前时间
    const now = new Date();
    // 获取当前时间的时间戳（毫秒）
    const localDate = now.getTime(); // 使用当前时间的毫秒级时间戳
    try {
      const params = {
        planId,
        userId,
        localDate,
      };
      // console.log('开始获取今日热门话题数据，方案ID：', planId);

      // 尝试从API获取数据
      let res;
      try {
        res = await getTrendingTodayApi(params);
        // console.log('今日热门话题数据接口返回：', res);
      } catch (error) {
        console.log('热门话题API调用失败，使用测试数据');
      }

      // 如果API没有返回数据，使用测试数据
      if (!res) {
        console.log('使用热门话题测试数据进行排序测试');
        // 模拟数据用于测试排序
        res = {
          results: [
            { token: '角色背景设定', negativeProportion: 0.00 },
            { token: '完成度', negativeProportion: 0.00 },
            { token: '提交内容', negativeProportion: 0.00 },
            { token: '画风', negativeProportion: 0.00 },
            { token: '宣传图质量', negativeProportion: 100.00 },
            { token: '游戏平衡性', negativeProportion: 45.50 },
            { token: '操作体验', negativeProportion: 23.80 }
          ]
        };
      }

      // 检查数据结构，兼容不同的返回格式
      let resultsData = [];

      if (res.code === 200 && res.result?.results) {
        // 如果是 {code: 200, result: {results: Array}} 格式
        resultsData = res.result.results;
      } else if (res.results && Array.isArray(res.results)) {
        // 如果是 {results: Array} 格式
        resultsData = res.results;
      } else {
        // console.error('接口返回数据格式无法解析：', res);
        return;
      }

      // 按负面占比排序后更新热门话题数据，限制显示5条
      const sortedResults = [...resultsData].sort((a, b) => {
        const aValue = parseFloat(a.negativeProportion) || 0;
        const bValue = parseFloat(b.negativeProportion) || 0;
        return bValue - aValue; // 降序排列，负面占比高的在前
      });

      newsList_topic.value = sortedResults.slice(0, 5).map((item) => ({
        title: item.token,
        views: item.negativeProportion,
      }));
      newsChartList_topic.value = sortedResults.slice(0, 30).map((item) => ({
        title: item.token,
        views: item.negativeProportion,
      }));

      // // 更新话题词云图
      // if (topicWordCloudChart) {
      //   topicWordCloudChart.setOption({
      //     series: [
      //       {
      //         data: newsList_topic.value.map((item) => ({
      //           name: item.title,
      //           value: parseInt(item.views.replace('%', '')) || 1, // 处理百分比格式
      //         })),
      //       },
      //     ],
      //   });
      // }
    } catch (error) {
      // console.error('获取今日热门话题数据异常：', error);
    }
  };

  // 清空所有数据的方法
  const clearAllData = () => {
    // 重置舆情数据对象
    publicOpinionData.value = {
      publicOpinion: 0,
      publicOpinionToday: 0,
      participantsNumber: 0,
      sumPositive: 0,
      sumNegative: 0,
    };
    sourceList.value = [];
    newsList.value = [];
    newsChartList.value = [];
    newsList_topic.value = []; // 清空今日热门话题数据
    newsChartList_topic.value = []; // 清空今日热门话题词云数据
    trendingList.value = [];
    trendingChartList.value = [];
    hotPostsData.value = [];
    currentPage.value = 1;
    hasMore.value = true;
    lastPostTime.value = '';
  };

  // 处理游戏信息更新
  const handleGameInfoUpdate = (gameData) => {
    // 游戏切换时先清空所有数据
    clearAllData();

    // 注意：这里不调用数据获取方法，因为此时可能还没有planId
    // 数据获取会在handlePlanUpdate中进行
  };

  // 处理方案更新
  const handlePlanUpdate = async (planData) => {
    if (planData.planId && planData.planId !== '') {
      // 有方案时，更新localStorage并获取数据
      localStorage.setItem('currentPlanId', planData.planId);
      // 先获取今日舆情数据
      await fetchPublicOpinionData(planData.gameId, planData.planId);
      // 再获取饼图数据
      await fetchPieData(planData.gameId, planData.planId);
      // 获取今日热点数据
      await fetchHotTodayData(planData.gameId, planData.planId);
      // 获取今日热门话题数据
      await fetchTrendingTodayData(planData.gameId, planData.planId);
      // 获取热帖数据 - 一次性加载前两页
      await fetchHotPostData(planData.gameId, planData.planId, false, 2);
      console.log('22222222');

      // 等待DOM更新后调整高度
      await nextTick();
      setTimeout(() => {
        adjustRightColumnHeight();
      }, 100);
    } else {
      // 没有方案时，清空所有数据
      clearAllData();
      // 清理localStorage
      localStorage.removeItem('currentPlanId');
    }
  };

  // 舆情数据
  const publicOpinionData = ref({
    publicOpinion: 0,
    publicOpinionToday: 0,
    participantsNumber: 0,
    sumPositive: 0,
    sumNegative: 0,
  });

  // ECharts 初始化
  // let myChart, newsWordCloudChart, topicWordCloudChart;

  // 查看更多方法
  const viewMore = (section) => {
    // 查看更多功能
  };

  // ECharts 初始化
  // let newsWordCloudChart, topicWordCloudChart;

  // onMounted(() => {
  //获取舆情渠道
  // fetchPieData();
  // 今日热点词云图
  // const newsWordCloudDom = document.getElementById('newsWordCloudChart');
  // newsWordCloudChart = echarts.init(newsWordCloudDom);
  // const newsWordCloudData = newsList.value.map((item) => ({
  //   name: item.title,
  //   value: parseInt(item.views) || 1, // 确保有默认值，避免 NaN
  // }));
  // const newsWordCloudOption = {
  //   tooltip: { show: true },
  //   series: [
  //     {
  //       type: 'wordCloud',
  //       shape: 'circle',
  //       sizeRange: [12, 60],
  //       rotationRange: [0, 0],
  //       rotationStep: 45,
  //       gridSize: 8,
  //       drawOutOfBound: false,
  //       textStyle: {
  //         color: () => {
  //           return `rgb(${Math.round(Math.random() * 255)}, ${Math.round(Math.random() * 255)}, ${Math.round(Math.random() * 255)})`;
  //         },
  //       },
  //       data: newsWordCloudData,
  //     },
  //   ],
  // };
  // newsWordCloudChart.setOption(newsWordCloudOption);
  //
  // // 监听 newsList 变化更新热点词云图
  // watch(
  //   newsList,
  //   () => {
  //     if (newsWordCloudChart) {
  //       newsWordCloudChart.setOption({
  //         series: [
  //           {
  //             data: newsList.value.map((item) => ({
  //               name: item.title,
  //               value: parseInt(item.views) || 1, // 确保有默认值
  //             })),
  //           },
  //         ],
  //       });
  //     }
  //   },
  //   { deep: true }
  // );
  // 今日热门话题词云图
  // const topicWordCloudDom = document.getElementById('topicWordCloudChart');
  // let topicWordCloudChart = echarts.init(topicWordCloudDom);
  // const topicWordCloudData = newsList_topic.value.map((item) => ({
  //   name: item.title,
  //   value: parseInt(item.views.replace('%', '')) || 1, // 处理百分比格式
  // }));
  // const topicWordCloudOption = {
  //   tooltip: { show: true },
  //   series: [
  //     {
  //       type: 'wordCloud',
  //       shape: 'circle',
  //       sizeRange: [12, 60],
  //       rotationRange: [0, 0],
  //       rotationStep: 45,
  //       gridSize: 8,
  //       drawOutOfBound: false,
  //       textStyle: {
  //         color: () => {
  //           return `rgb(${Math.round(Math.random() * 255)}, ${Math.round(Math.random() * 255)}, ${Math.round(Math.random() * 255)})`;
  //         },
  //       },
  //       data: topicWordCloudData,
  //     },
  //   ],
  // };
  // topicWordCloudChart.setOption(topicWordCloudOption);
  // 监听 newsList_topic 变化更新话题词云图
  // watch(
  //   newsList_topic,
  //   () => {
  //     if (topicWordCloudChart) {
  //       topicWordCloudChart.setOption({
  //         series: [
  //           {
  //             data: newsList_topic.value.map((item) => ({
  //               name: item.title,
  //               value: parseInt(item.views.replace('%', '')) || 1, // 处理百分比格式
  //             })),
  //           },
  //         ],
  //       });
  //     }
  //   },
  //   { deep: true }
  // );
  // 在组件挂载时不再直接调用 fetchPublicOpinionData
  // });

  // onUnmounted(() => {
  //   // if (myChart) {
  //   //   myChart.dispose();
  //   // }
  //   if (newsWordCloudChart) {
  //     newsWordCloudChart.dispose();
  //   }
  //   if (topicWordCloudChart) {
  //     topicWordCloudChart.dispose();
  //   }
  // });

  // 添加计算属性
  const showRecentPostsTip = computed(() => {
    return publicOpinionData.value.publicOpinion === 0;
  });
</script>

<style scoped>
  /* 页面整体布局 */
  .page-container {
    font-family: Arial, sans-serif;
    width: 100%;
    padding: 20px;
    background-color: #f9f9f9;
  }

  /* 顶部外框 */
  .public-wrapper {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* 顶部栏 */
  .top-bar {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .game-info {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .logo {
    width: 70px;
    height: 70px;
    border-radius: 10px;
  }

  .game-details {
    display: flex;
    flex-direction: column;
  }

  .game-title {
    font-size: 20px;
    font-weight: bold;
    margin: 0;
  }

  .game-subtitle {
    font-size: 14px;
    color: gray;
    margin: 0;
  }

  /* 竖线样式 */
  .vertical-line {
    width: 1px;
    height: 80px;
    background-color: #ccc;
    margin-left: 50px;
    margin-right: 50px;
  }

  /* 搜索栏 */
  .search-bar {
    flex: 1;
    width: 500px;
    margin: 0 20px;
  }

  .search-container {
    position: relative;
    display: flex;
    align-items: center;
  }

  .search-icon {
    position: absolute;
    left: 10px;
    color: #888;
    font-size: 16px;
  }

  .search-input {
    padding: 8px 8px 8px 40px;
    border: 1px solid #ccc;
    border-radius: 50px;
    width: 100%;
    font-size: 16px;
    background-color: #f1f3f5;
  }

  /* 方案栏 */
  .prog-bar {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: auto;
  }

  .select-plan {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 5px;
    width: 200px;
  }

  /* 配置监控方案按钮样式 */
  .configure-button {
    color: #018ffb; /* 蓝色 */
    font-size: 14px;
    padding: 0; /* 去掉默认内边距，保持文字按钮风格 */
    background: transparent; /* 透明背景 */
    border: none; /* 去掉边框 */
    cursor: pointer;
    margin-left: 10px; /* 与选择框保持一定距离 */
  }

  .configure-button:hover {
    color: #0170c9; /* 悬停时颜色变深 */
    text-decoration: underline; /* 悬停时添加下划线 */
  }

  /* 舆情数据 */
  .today-public-opinion {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  .today-opinion-item {
    flex: 1;
    margin: 0 10px;
  }

  .today-opinion-box {
    display: flex;
    align-items: center;
    padding: 20px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .today-opinion-image {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    margin-right: 15px;
  }

  .today-opinion-text {
    text-align: left;
    flex: 1;
  }

  .today-opinion-text h3 {
    font-size: 16px;
    color: #666;
    margin-bottom: 5px;
  }

  .today-opinion-text p {
    font-size: 24px;
    font-weight: bold;
    color: #333;
  }

  .today-opinion-box h3,
  .today-opinion-box p {
    margin: 0;
  }

  .more-icon {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
  }

  .ellipsis-button:hover .more-icon {
    transform: scale(1.2);
  }

  /* 左右两列布局 */
  .main-content {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    align-items: flex-start; /* 改为顶部对齐，不强制拉伸 */
  }

  .left-column {
    flex: 2; /* 左半边占 2/3 宽度 */
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .right-column {
    flex: 1; /* 右半边占 1/3 宽度 */
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    /* 让右侧容器高度自然适应左侧内容高度 */
    height: fit-content;
  }

  .header-with-more {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  /* 标题样式 */
  .opinion-source h2,
  .hot-news h2,
  .hot-topic h2,
  .right-column h2 {
    font-size: 20px;
    font-weight: bold;
    margin: 0;
    color: #333;
  }

  .opinion-source h2 {
    border-bottom: 4px solid #018ffb;
    width: 80px;
  }

  .hot-news h2 {
    border-bottom: 4px solid #018ffb;
    width: 80px;
  }

  .hot-topic h2 {
    border-bottom: 4px solid #018ffb;
    width: 120px;
  }

  .right-column h2 {
    border-left: 4px solid #018ffb;
    padding-left: 10px;
    height: 30px;
    margin-bottom: 15px;
    font-size: 22px;
    display: flex;
    align-items: center;
  }

  /* 查看更多按钮样式 */
  .more-button {
    color: #018ffb; /* 蓝色，与页面其他蓝色元素一致 */
    font-size: 14px;
    padding: 0; /* 去掉默认内边距，保持文字按钮风格 */
    background: transparent; /* 透明背景 */
    border: none; /* 去掉边框 */
    cursor: pointer;
  }

  .more-button:hover {
    color: #0170c9; /* 悬停时颜色变深，与 view-more 按钮一致 */
    text-decoration: underline; /* 悬停时添加下划线 */
  }

  /* 滚动指示器样式 */
  .scroll-indicator {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #018ffb;
    animation: pulse 2s infinite;
  }

  .scroll-icon {
    margin-right: 5px;
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.6;
    }
  }

  /* 热帖列表样式 */
  .hot-posts-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #ccc #f1f1f1;
    padding-right: 5px;
    margin-top: 10px;
    /* 高度将通过JavaScript动态设置 */
    position: relative; /* 添加相对定位，使加载容器能够正确定位 */
  }

  /* 自定义滚动条样式 */
  .hot-posts-list::-webkit-scrollbar {
    width: 6px;
  }

  .hot-posts-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .hot-posts-list::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
  }

  .hot-posts-list::-webkit-scrollbar-thumb:hover {
    background: #aaa;
  }

  .hot-post-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 8px;
    border-bottom: 1px solid #eee;
    transition: all 0.2s ease;
    border-radius: 6px;
    margin-bottom: 2px;
  }

  .hot-post-item:hover {
    background-color: #f0f7ff;
    transform: translateX(3px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  .hot-post-item:nth-child(-n + 3) {
    background-color: #f9f9f9;
  }

  .post-rank {
    font-size: 18px;
    font-weight: bold;
    color: #018ffb; /* 编号为蓝色 */
    width: 30px; /* 固定宽度，确保对齐 */
    text-align: center;
    background-color: #f0f7ff;
    border-radius: 50%;
    height: 30px;
    line-height: 30px;
    display: inline-block;
  }

  .hot-post-item:nth-child(-n + 3) .post-rank {
    color: #fff;
    background-color: #e74c3c;
  }

  .post-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px; /* 标题和日期之间的间距 */
  }

  .post-title-link {
    text-decoration: none;
    color: inherit;
    cursor: pointer;
    display: block;
  }

  .post-title {
    font-size: 16px;
    color: #333; /* 标题为黑色 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
    display: block;
    transition: color 0.2s ease;
  }

  .hot-post-item:hover .post-title {
    color: #018ffb;
  }

  .hot-post-item:nth-child(-n + 3) .post-title {
    font-weight: 600;
  }

  .post-date {
    font-size: 14px;
    color: #666; /* 日期为灰色 */
  }

  .post-views-container {
    display: flex;
    align-items: center;
    background-color: #fff5f5;
    padding: 2px 8px;
    border-radius: 12px;
    margin-left: 5px;
  }

  .post-views-icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }

  .post-views {
    font-size: 14px;
    color: #e74c3c; /* 浏览量为红色 */
    min-width: 40px; /* 固定宽度，确保对齐 */
    text-align: right;
    font-weight: 500;
  }

  /* 舆情渠道 */
  .opinion-source {
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  #sourceChart {
    width: 100%;
    height: 400px;
  }

  /* 热点和话题布局 */
  .hot-news,
  .hot-topic {
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .hot-layout {
    display: flex;
    gap: 20px;
    margin-top: 20px;
  }

  .hot-table-container {
    flex: 1; /* 左半边占 2/3 宽度 */
  }

  .hot-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
  }

  .hot-table th,
  .hot-table td {
    padding: 10px;
    text-align: center;
    border-bottom: 1px solid #eee;
  }

  .hot-table th {
    background-color: #c2e8f8;
    font-weight: bold;
    color: #333;
  }

  .hot-table td {
    color: #666;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* 斑马纹样式 */
  .hot-table tr:nth-child(odd) {
    background-color: #ffffff; /* 奇数行（白色） */
  }

  .hot-table tr:nth-child(even) {
    background-color: #dcf2fb; /* 偶数行（浅灰色） */
  }

  /* 前三名的特殊样式 */
  .top-rank {
    color: #e74c3c !important; /* 前三名为红色 */
    font-weight: bold;
  }

  /* 表格前三行的特殊样式 */
  .top-item td {
    color: #e74c3c !important;
    font-weight: bold;
  }

  .hot-table tr:hover {
    background-color: #e6f7ff; /* 悬停时覆盖斑马纹，保持一致的高亮效果 */
  }

  .word-cloud-container {
    flex: 1; /* 右半边占 2/3 宽度 */
  }

  /* //#newsWordCloudChart,
//#topicWordCloudChart {
//  width: 100%;
//  height: 300px;
//}*/

  .loading-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.8); /* 恢复为白色半透明背景 */
    z-index: 10;
  }

  .loading-more {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #0056b3;
    font-size: 16px;
    font-weight: bold;
    animation: float 3s infinite ease-in-out;
  }

  .loading-logo-img {
    width: 100px;
    height: 100px;
    margin-bottom: 15px;
    animation: boat-rock 3s infinite ease-in-out;
    transform-origin: bottom center;
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes boat-rock {
    0%,
    100% {
      transform: rotate(-5deg) scale(1.05);
    }
    50% {
      transform: rotate(5deg) scale(1.15);
    }
  }

  .no-more-data {
    text-align: center;
    padding: 16px;
    color: #999;
    font-size: 14px;
  }

  /* 响应式设计 - 保持flex动态适应 */
  @media (max-width: 1200px) {
    .main-content {
      flex-direction: column; /* 小屏幕时改为垂直布局 */
    }

    .left-column,
    .right-column {
      flex: none; /* 取消flex比例 */
      width: 100%; /* 全宽显示 */
    }

    .right-column {
      margin-top: 20px;
    }

    /* 小屏幕时为热帖列表设置合理的高度限制 */
    .hot-posts-list {
      max-height: 500px;
      min-height: 400px; /* 与JavaScript中的最小高度保持一致 */
    }
  }

  @media (max-width: 768px) {
    .main-content {
      gap: 15px;
      margin-bottom: 15px;
    }

    .right-column {
      padding: 15px;
      margin-top: 15px;
    }

    .hot-posts-list {
      max-height: 400px;
      min-height: 350px; /* 移动端稍微减少但仍保证加载动画显示空间 */
    }
  }

  .title-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .recent-posts-tip {
    position: relative;
    background: rgba(255, 243, 224, 0.6);
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid rgba(255, 152, 0, 0.1);
    box-shadow: 0 2px 6px rgba(255, 152, 0, 0.04);
    animation: fadeIn 0.5s ease-out;
    backdrop-filter: blur(4px);
  }

  .recent-posts-tip::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 152, 0, 0.1) 50%, transparent 100%);
  }

  .tip-content {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .tip-icon {
    font-size: 14px;
    color: rgba(255, 152, 0, 0.6);
    margin-right: 8px;
    animation: gentleFloat 4s ease-in-out infinite;
  }

  .tip-text {
    font-size: 13px;
    color: rgba(230, 81, 0, 0.7);
    font-weight: normal;
    letter-spacing: 0.2px;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes gentleFloat {
    0%,
    100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-2px);
    }
  }

  /* 简化悬停效果 */
  .recent-posts-tip:hover {
    background: rgba(255, 243, 224, 0.8);
    transition: background 0.3s ease;
  }

  .recent-posts-tip:hover .tip-icon {
    color: rgba(255, 152, 0, 0.8);
  }
</style>
