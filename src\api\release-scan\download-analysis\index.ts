import { defHttp } from '/@/utils/http/axios';

enum Api{
    getAllDevice = '/shop/getAllDevice',
    // getDownloadCompare = '/shop/getDownloadCompare',
    getDownloadCompareRank = '/shop/getDownloadCompareRank',
    getAllCountry = '/shop/getAllCountry',
    // downloadVolume = '/releaseScans/downloadDataAnalysis/downloadVolume',
    gameDimension = '/shop/competitorAnalysis/gameDimension'
}

// 获取设备列表
export function getAllDeviceApi(){
    return defHttp.get({
        url: Api.getAllDevice
    })
}

// 获取国家列表
export function getAllCountryApi(){
    return defHttp.get({
        url: Api.getAllCountry
    })
}

// 获取下载量分析
export function gameDimensionApi(data){
    return defHttp.post({
        url: Api.gameDimension,
        data
    })
}

// // 
// export function getDownloadCompareRankApi(params){
//     return defHttp.get({
//         url: Api.getDownloadCompareRank,
//         params
//     })
// }

